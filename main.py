import sys
import threading
import ctypes
from pynput import keyboard

# PyQt5 imports
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTextEdit, QFrame,
                            QMessageBox, QComboBox, QMenu, QAction)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt5.QtGui import QFont

from audio_handler import AudioHandler
from api_client import APIClient
from ui_manager import UIManager

# Import stealth configuration
try:
    from stealth_config import WINDOW_TRANSPARENCY, ENABLE_SCREEN_CAPTURE_HIDING, ENABLE_TASKBAR_HIDING
except ImportError:
    # Default values if config file doesn't exist
    WINDOW_TRANSPARENCY = 0.9
    ENABLE_SCREEN_CAPTURE_HIDING = True
    ENABLE_TASKBAR_HIDING = True

class KeyboardSignals(QObject):
    """Signals for keyboard events"""
    caps_lock_signal = pyqtSignal()
    arrow_key_signal = pyqtSignal(str)  # direction
    toggle_visibility_signal = pyqtSignal()  # Ctrl+Space toggle

class UISignals(QObject):
    """Signals for UI updates"""
    update_status_signal = pyqtSignal(str)
    update_response_signal = pyqtSignal(str)
    clear_response_signal = pyqtSignal()

class InterviewApp(QWidget):
    def __init__(self):
        super().__init__()

        # Initialize keyboard state variables
        self.caps_lock_pressed = False
        self.shift_pressed = False
        self.alt_pressed = False
        self.ctrl_pressed = False
        self.space_pressed = False
        self.pressed_keys = set()

        # Initialize window state variables
        self.is_window_visible = True

        # Initialize keyboard signals
        self.keyboard_signals = KeyboardSignals()
        self.keyboard_signals.caps_lock_signal.connect(self.handle_caps_lock_press)
        self.keyboard_signals.arrow_key_signal.connect(self.handle_arrow_key)
        self.keyboard_signals.toggle_visibility_signal.connect(self.toggle_window_visibility)

        # Initialize UI signals
        self.ui_signals = UISignals()
        self.ui_signals.update_status_signal.connect(self.update_status_bar)
        self.ui_signals.update_response_signal.connect(self._update_streaming_response)
        self.ui_signals.clear_response_signal.connect(self.clear_ai_response)

        # Window setup with ABID AI exact approach
        self.setWindowTitle("Real-time Interview Assistant")
        # Window flags without click-through
        self.original_flags = Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool
        self.setWindowFlags(self.original_flags)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Make window smaller and more compact
        self.resize(500, 400)
        self.setMinimumSize(400, 300)

        # Position window in bottom-right corner of screen
        self.position_window_bottom_right()

        # Setup UI
        self.setup_ui()

        # Setup keyboard listener (ABID AI approach)
        self.setup_keyboard_listener()

        # Hide from screen capture
        self.hide_from_capture()

        # Initialize components (skip UI manager since we're using PyQt5 directly)
        # Enable automatic device detection by default
        self.audio_handler = AudioHandler(self.handle_audio_event, self.get_selected_device_index, auto_device_mode=True)
        try:
            self.api_client = APIClient()
        except ValueError as e:
            QMessageBox.critical(self, "API Key Error", str(e))
            sys.exit()
            return

        self.populate_audio_devices()

        # Set up timer for auto device mode updates
        self.device_update_timer = QTimer()
        self.device_update_timer.timeout.connect(self.update_device_display)
        self.device_update_timer.start(3000)  # Update every 3 seconds

        # Set initial status message
        self.ui_signals.update_status_signal.emit("Status: Ready - Enhanced interview question capture enabled")

    def setup_ui(self):
        """Setup the main UI"""
        # Main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # --- Controls Frame ---
        controls_frame = QFrame()
        controls_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                padding: 5px;
                margin: 2px;
            }
        """)
        controls_layout = QHBoxLayout()

        self.start_button = QPushButton("Start Real-time Listening")
        self.start_button.clicked.connect(self.start_listening)
        # Make buttons receive mouse events even with click-through enabled
        self.start_button.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        controls_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Listening")
        self.stop_button.clicked.connect(self.stop_listening)
        self.stop_button.setEnabled(False)
        # Make buttons receive mouse events even with click-through enabled
        self.stop_button.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        controls_layout.addWidget(self.stop_button)

        # Audio device status and control
        device_label = QLabel("Audio Input:")
        controls_layout.addWidget(device_label)

        # Auto device mode toggle
        self.auto_device_button = QPushButton("🎤 Auto Detection: ON")
        self.auto_device_button.clicked.connect(self.toggle_auto_device_mode)
        self.auto_device_button.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        self.auto_device_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        controls_layout.addWidget(self.auto_device_button)

        # Device status display (replaces dropdown in auto mode)
        self.device_status_label = QLabel("🔍 Detecting audio device...")
        self.device_status_label.setStyleSheet("QLabel { color: #666; font-style: italic; }")
        controls_layout.addWidget(self.device_status_label)

        # Manual device dropdown (hidden by default in auto mode)
        self.device_dropdown = QComboBox()
        self.device_dropdown.setMinimumWidth(200)
        self.device_dropdown.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        self.device_dropdown.setVisible(False)  # Hidden in auto mode
        controls_layout.addWidget(self.device_dropdown)

        controls_frame.setLayout(controls_layout)

        # --- AI Response Frame (Combined Q&A) ---
        ai_response_frame = QFrame()
        ai_response_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                padding: 5px;
                margin: 2px;
            }
        """)
        ai_response_layout = QVBoxLayout()

        # AI Response label
        ai_response_label = QLabel("Interview Q&A Assistant")
        ai_response_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333333;
                padding: 5px;
            }
        """)
        ai_response_layout.addWidget(ai_response_label)

        # AI Response text widget with ABID AI stealth configuration
        self.ai_response_text_widget = QTextEdit()
        self.ai_response_text_widget.setStyleSheet("""
            QTextEdit {
                background-color: #000000;
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', sans-serif;
                border: 2px solid #333333;
                border-radius: 10px;
                padding: 15px;
                line-height: 1.6;
                selection-background-color: transparent;
                selection-color: #FFFFFF;
                cursor: default;
            }
            QTextEdit:hover {
                cursor: default;
            }
            QTextEdit:focus {
                outline: none;
                border: 2px solid #333333;
                cursor: default;
            }
            QScrollBar:vertical {
                background-color: #2b2b2b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """)
        self.ai_response_text_widget.setPlainText("🤖 AI interview assistant ready...\n\nI'll provide helpful responses as an expert software developer during your technical interview.")
        self.ai_response_text_widget.setReadOnly(True)

        # Apply ABID AI stealth settings to response area
        self.ai_response_text_widget.setFocusPolicy(Qt.NoFocus)  # Prevent focus to avoid blinking
        self.ai_response_text_widget.setTextInteractionFlags(Qt.NoTextInteraction)  # Disable text interaction

        # Disable cursor blinking completely
        self.ai_response_text_widget.setCursorWidth(0)  # Hide cursor completely
        self.ai_response_text_widget.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.ai_response_text_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Prevent any selection or editing
        self.ai_response_text_widget.setTextInteractionFlags(Qt.NoTextInteraction)
        self.ai_response_text_widget.setContextMenuPolicy(Qt.NoContextMenu)  # Disable right-click menu

        # Additional stealth settings - make response area completely non-interactive
        self.ai_response_text_widget.setEnabled(True)  # Keep enabled but override events
        self.ai_response_text_widget.setAcceptDrops(False)  # No drag and drop

        # Force arrow cursor permanently - this is the key fix
        self.ai_response_text_widget.setCursor(Qt.ArrowCursor)
        self.ai_response_text_widget.viewport().setCursor(Qt.ArrowCursor)  # Also set on viewport

        # Override all mouse and keyboard events to prevent any interaction
        self.ai_response_text_widget.keyPressEvent = lambda event: event.ignore()
        self.ai_response_text_widget.keyReleaseEvent = lambda event: event.ignore()
        self.ai_response_text_widget.focusInEvent = lambda event: self.ai_response_text_widget.clearFocus()
        self.ai_response_text_widget.focusOutEvent = lambda event: None

        # Set up a timer to periodically enforce cursor behavior
        self.cursor_timer = QTimer()
        self.cursor_timer.timeout.connect(self.enforce_cursor_behavior)
        self.cursor_timer.start(100)  # Check every 100ms to reduce overhead

        ai_response_layout.addWidget(self.ai_response_text_widget)
        ai_response_frame.setLayout(ai_response_layout)

        # --- Status Bar Frame ---
        bottom_frame = QFrame()
        bottom_layout = QHBoxLayout()

        self.status_bar = QLabel("Status: Idle")
        self.status_bar.setStyleSheet("""
            QLabel {
                border: 1px solid #cccccc;
                padding: 5px;
                background-color: #f0f0f0;
            }
        """)
        bottom_layout.addWidget(self.status_bar)

        # Add resize handle in bottom right corner
        self.resize_handle = QLabel("⋰")
        self.resize_handle.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-family: Arial;
                padding: 2px;
            }
        """)
        self.resize_handle.setCursor(Qt.SizeFDiagCursor)
        # Make resize handle receive mouse events even with click-through enabled
        self.resize_handle.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        bottom_layout.addWidget(self.resize_handle)
        self.setup_resize_handle()

        bottom_frame.setLayout(bottom_layout)

        # Add all frames to main layout
        layout.addWidget(controls_frame)
        layout.addWidget(ai_response_frame)
        layout.addWidget(bottom_frame)

        self.setLayout(layout)

        # Apply transparency to the main window (ABID AI approach)
        self.setWindowOpacity(WINDOW_TRANSPARENCY)

        # Apply main window styling
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #f0f0f0, stop:1 #e0e0e0);
                border-radius: 15px;
            }
        """)

    def hide_from_capture(self):
        """Hide window from screen capture (Windows only) - ABID AI approach"""
        try:
            hwnd = int(self.winId())

            # Set window to be excluded from capture
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)

            if result:
                print("✅ Window successfully hidden from screen capture")
            else:
                print("❌ Failed to hide window from screen capture")

        except Exception as e:
            print(f"❌ Error hiding from capture: {e}")

    def enforce_cursor_behavior(self):
        """Periodically enforce arrow cursor behavior on response area - ABID AI approach"""
        try:
            if hasattr(self, 'ai_response_text_widget') and self.ai_response_text_widget:
                # Always force arrow cursor on both widget and viewport
                self.ai_response_text_widget.setCursor(Qt.ArrowCursor)
                self.ai_response_text_widget.viewport().setCursor(Qt.ArrowCursor)

                # Always ensure no focus to prevent blinking
                if self.ai_response_text_widget.hasFocus():
                    self.ai_response_text_widget.clearFocus()

                # Ensure text interaction is disabled
                self.ai_response_text_widget.setTextInteractionFlags(Qt.NoTextInteraction)

                # Ensure focus policy is maintained
                self.ai_response_text_widget.setFocusPolicy(Qt.NoFocus)

        except Exception as e:
            # Silently handle errors to avoid spam
            pass

    def populate_audio_devices(self):
        devices = self.audio_handler.get_available_input_devices()

        if self.audio_handler.auto_device_mode:
            # In auto mode, update status label instead of dropdown
            current_device = self.audio_handler.get_current_device_index()
            if current_device is not None and devices:
                current_device_name = next((name for idx, name in devices if idx == current_device), "Unknown Device")
                # Clean up device name for display
                display_name = current_device_name[:50] + "..." if len(current_device_name) > 50 else current_device_name
                self.device_status_label.setText(f"🎤 Using: {display_name}")
                self.device_status_label.setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; }")
            else:
                self.device_status_label.setText("🔍 Detecting audio device...")
                self.device_status_label.setStyleSheet("QLabel { color: #666; font-style: italic; }")
        else:
            # Manual mode - show dropdown and hide status label
            device_names = [f"{idx}: {name}" for idx, name in devices]
            if device_names:
                self.device_dropdown.clear()
                self.device_dropdown.addItems(device_names)
                self.device_dropdown.setCurrentIndex(0)  # Default to first device
                self.start_button.setEnabled(True)
            else:
                self.device_dropdown.clear()
                self.device_dropdown.addItem("No input devices found")
                self.start_button.setEnabled(False)

    def toggle_auto_device_mode(self):
        """Toggle between automatic and manual device selection."""
        if self.audio_handler.auto_device_mode:
            # Switch to manual mode
            self.audio_handler.disable_auto_device_mode()
            self.auto_device_button.setText("🔧 Manual Selection: ON")
            self.auto_device_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")

            # Show dropdown, hide status label
            self.device_dropdown.setVisible(True)
            self.device_dropdown.setEnabled(True)
            self.device_status_label.setVisible(False)

            self.update_status_bar("Status: Manual device selection enabled")
        else:
            # Switch to auto mode
            self.audio_handler.enable_auto_device_mode()
            self.auto_device_button.setText("🎤 Auto Detection: ON")
            self.auto_device_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")

            # Hide dropdown, show status label
            self.device_dropdown.setVisible(False)
            self.device_dropdown.setEnabled(False)
            self.device_status_label.setVisible(True)

            self.update_status_bar("Status: Automatic device detection enabled")

        # Refresh device display
        self.populate_audio_devices()

    def update_device_display(self):
        """Periodically update device display in auto mode."""
        if self.audio_handler.auto_device_mode:
            self.populate_audio_devices()

    def get_selected_device_index(self):
        # In auto mode, this method is not used as AudioHandler uses its own device detection
        if self.audio_handler.auto_device_mode:
            return self.audio_handler.get_current_device_index()

        selected_device_str = self.device_dropdown.currentText()
        if selected_device_str and ":" in selected_device_str:
            try:
                return int(selected_device_str.split(":")[0])
            except ValueError:
                return None # Or default device index like 0 or None
        return None # Default if no valid selection

    def handle_audio_event(self, event_type, data):
        """Callback function for AudioHandler events."""
        if event_type == "transcription":
            # Store the question for Q&A format
            if data and not data.startswith("[") and len(data.strip()) > 3: # Improved validation
                self.current_question = data
                print(f"📝 Received transcription: {data}")
                # Run API call in a new thread to avoid blocking UI
                threading.Thread(target=self.fetch_ai_response, args=(data,), daemon=True).start()
            else:
                print(f"⚠️ Skipping invalid transcription: {data}")
        elif event_type == "status":
            self.ui_signals.update_status_signal.emit(data)
        elif event_type == "error":
            print(f"❌ Audio error: {data}")
            self.ui_signals.update_status_signal.emit(f"Status: Error - {data}")
            # Only show critical errors to user, not every minor issue
            if "device" in data.lower() or "stream" in data.lower():
                QMessageBox.critical(self, "Audio Error", data)
                self.stop_listening_ui_update()

    def update_status_bar(self, text):
        """Update status bar text"""
        self.status_bar.setText(text)

    def clear_ai_response(self):
        """Clear AI response text widget"""
        self.ai_response_text_widget.setReadOnly(False)
        self.ai_response_text_widget.clear()
        self.ai_response_text_widget.setReadOnly(True)

    def fetch_ai_response(self, text):
        # Use signals to ensure UI updates happen in main thread
        self.ui_signals.update_status_signal.emit("Status: Analyzing question...")

        # Show question immediately for faster response
        self.current_question = text
        question_text = f"🎤 Question: {text}\n\n💭 Thinking...\n"
        self.ui_signals.update_response_signal.emit(question_text)

        # Define streaming callback for real-time updates
        def streaming_callback(partial_response):
            # Update UI with streaming response
            full_text = f"🎤 Question: {text}\n\n✅ Answer:\n{partial_response}"
            self.ui_signals.update_response_signal.emit(full_text)

        try:
            # Get AI response with streaming
            print(f"🤖 Requesting AI response for: {text[:50]}...")
            ai_response = self.api_client.get_ai_response(text, streaming_callback)

            # Update with final response
            if ai_response and not ai_response.startswith("["):
                final_text = f"🎤 Question: {text}\n\n✅ Answer:\n{ai_response}"
                self.ui_signals.update_response_signal.emit(final_text)
                print(f"✅ AI response delivered successfully")
            elif ai_response:
                # Handle error responses
                error_text = f"🎤 Question: {text}\n\n❌ Error:\n{ai_response}"
                self.ui_signals.update_response_signal.emit(error_text)
                print(f"❌ AI response error: {ai_response}")
            else:
                # No response received
                error_text = f"🎤 Question: {text}\n\n❌ No response received from AI"
                self.ui_signals.update_response_signal.emit(error_text)
                print(f"❌ No AI response received")

        except Exception as e:
            print(f"❌ Error fetching AI response: {e}")
            error_text = f"🎤 Question: {text}\n\n❌ Error getting response: {str(e)}"
            self.ui_signals.update_response_signal.emit(error_text)

        # Update status only if not currently listening (to avoid overwriting listening status)
        if not self.audio_handler.is_listening:
            self.ui_signals.update_status_signal.emit("Status: Ready for next question")
        else:
            self.ui_signals.update_status_signal.emit("Status: Listening... (Response ready)")

    def _update_response_text(self, text):
        """Thread-safe method to update response text."""
        try:
            self.ai_response_text_widget.setReadOnly(False)
            self.ai_response_text_widget.clear()
            self.ai_response_text_widget.setPlainText(text)
            # Scroll to bottom
            cursor = self.ai_response_text_widget.textCursor()
            cursor.movePosition(cursor.End)
            self.ai_response_text_widget.setTextCursor(cursor)
            self.ai_response_text_widget.setReadOnly(True)
        except Exception as e:
            # Silently handle threading issues
            pass

    def _update_streaming_response(self, text):
        """Thread-safe method to update streaming response."""
        try:
            self.ai_response_text_widget.setReadOnly(False)
            self.ai_response_text_widget.clear()
            self.ai_response_text_widget.setPlainText(text)
            # Scroll to bottom
            cursor = self.ai_response_text_widget.textCursor()
            cursor.movePosition(cursor.End)
            self.ai_response_text_widget.setTextCursor(cursor)
            self.ai_response_text_widget.setReadOnly(True)
        except Exception as e:
            # Silently handle errors
            pass

    def start_listening(self):
        self.clear_ai_response()  # Only clear AI response, no transcription widget
        self.update_status_bar("Status: Starting enhanced question capture...")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # Disable device controls during listening
        self.auto_device_button.setEnabled(False)
        if not self.audio_handler.auto_device_mode:
            self.device_dropdown.setEnabled(False)

        # Clear cache to ensure fresh simple English responses
        if hasattr(self, 'api_client') and self.api_client:
            self.api_client.response_cache.clear()

        self.audio_handler.start_listening()
        # Status update will come from audio_handler via callback

    def stop_listening_ui_update(self): # Helper for UI changes on stop
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        # Re-enable device controls
        self.auto_device_button.setEnabled(True)
        if not self.audio_handler.auto_device_mode:
            self.device_dropdown.setEnabled(True)
        # Status update will come from audio_handler via callback or here if immediate

    def stop_listening(self):
        self.update_status_bar("Status: Stopping...")
        self.audio_handler.stop_listening()
        self.stop_listening_ui_update() # Update UI immediately
        # Further status updates (like 'processing') will come from audio_handler callback

    def position_window_bottom_right(self):
        """Position window in bottom-right corner of screen"""
        # Get screen dimensions
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # Get window dimensions
        window_width = 500
        window_height = 400

        # Calculate position (bottom-right with some margin)
        x = screen_width - window_width - 50
        y = screen_height - window_height - 100

        self.move(x, y)

    def setup_keyboard_listener(self):
        """Setup global keyboard listener - ABID AI approach"""
        try:
            self.keyboard_listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.keyboard_listener.start()
            print("✅ Keyboard listener started")
        except Exception as e:
            print(f"❌ Error starting keyboard listener: {e}")

    def on_key_press(self, key):
        """Handle key press events - ABID AI approach"""
        try:
            print(f"Key pressed: {key}")  # Debug output

            # Initialize pressed_keys set if it doesn't exist
            if not hasattr(self, 'pressed_keys'):
                self.pressed_keys = set()

            # Add key to pressed keys set
            self.pressed_keys.add(key)

            # Shift detection
            if key == keyboard.Key.shift or key == keyboard.Key.shift_r:
                self.shift_pressed = True
                print("Shift key pressed")

            # Ctrl detection
            elif key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                self.ctrl_pressed = True
                print("Ctrl key pressed")

            # Space detection for Ctrl+Space combination
            elif key == keyboard.Key.space:
                self.space_pressed = True
                if self.ctrl_pressed:
                    print("🔄 Ctrl+Space pressed - Toggling window visibility")
                    self.keyboard_signals.toggle_visibility_signal.emit()

            # Caps Lock detection - trigger listening toggle
            elif key == keyboard.Key.caps_lock:
                print("🔒 Caps Lock pressed - Handling caps lock")
                # Use signal to handle caps lock in main thread
                self.keyboard_signals.caps_lock_signal.emit()

            # Arrow keys - check if Ctrl is pressed
            elif key == keyboard.Key.up:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('up')

            elif key == keyboard.Key.down:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('down')

            elif key == keyboard.Key.left:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('left')

            elif key == keyboard.Key.right:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('right')

        except Exception as e:
            print(f"❌ Error in key press: {e}")

    def on_key_release(self, key):
        """Handle key release events - ABID AI approach"""
        try:
            # Remove key from pressed keys set
            if hasattr(self, 'pressed_keys') and key in self.pressed_keys:
                self.pressed_keys.remove(key)

            # Update key state flags
            if key == keyboard.Key.caps_lock:
                self.caps_lock_pressed = False
            elif key == keyboard.Key.shift or key == keyboard.Key.shift_r:
                self.shift_pressed = False
            elif key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                self.ctrl_pressed = False
                print("Ctrl key released")
            elif key == keyboard.Key.space:
                self.space_pressed = False
        except Exception as e:
            print(f"❌ Error in key release: {e}")

    def handle_caps_lock_press(self):
        """Handle Caps Lock press - toggle listening"""
        print("🔒 Caps Lock signal received - Toggling listening")
        self.toggle_listening()

    def handle_arrow_key(self, direction):
        """Handle arrow key press - move window"""
        print(f"🔄 Arrow key signal received - Moving window {direction}")
        self.move_window(direction)

    def toggle_window_visibility(self):
        """Toggle window visibility with Ctrl+Space"""
        if self.is_window_visible:
            self.hide_window()
        else:
            self.show_window()

    def hide_window(self):
        """Hide the window or move it to back"""
        print("🔄 Hiding window...")
        self.is_window_visible = False
        # Option 1: Hide the window completely
        self.hide()
        # Option 2: Alternative - lower the window (move to back)
        # self.lower()
        # self.setWindowOpacity(0.1)  # Make nearly transparent

    def show_window(self):
        """Show the window"""
        print("🔄 Showing window...")
        self.is_window_visible = True
        self.show()
        self.raise_()  # Bring to front
        self.activateWindow()  # Give focus
        self.setWindowOpacity(WINDOW_TRANSPARENCY)  # Restore original transparency

    def toggle_click_through(self, enable=None):
        """Toggle click-through functionality"""
        if enable is None:
            self.click_through_enabled = not self.click_through_enabled
        else:
            self.click_through_enabled = enable

        if self.click_through_enabled:
            print("🔄 Enabling click-through...")
            self.setWindowFlags(self.click_through_flags)
            self.show()  # Need to show after changing flags
            self.ui_signals.update_status_signal.emit("Status: Click-through enabled (default)")
        else:
            print("🔄 Disabling click-through...")
            self.setWindowFlags(self.original_flags)
            self.show()  # Need to show after changing flags
            self.ui_signals.update_status_signal.emit("Status: Click-through disabled")

    def move_window(self, direction):
        """Move window in the specified direction using Ctrl+Arrow keys"""
        # Get current window position
        current_x = self.x()
        current_y = self.y()

        # Get screen dimensions for boundary checking
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # Get window dimensions
        window_width = self.width()
        window_height = self.height()

        # Movement step size (pixels)
        step = 50

        # Calculate new position based on direction
        new_x = current_x
        new_y = current_y

        if direction == 'up':
            new_y = max(0, current_y - step)
        elif direction == 'down':
            new_y = min(screen_height - window_height, current_y + step)
        elif direction == 'left':
            new_x = max(0, current_x - step)
        elif direction == 'right':
            new_x = min(screen_width - window_width, current_x + step)

        # Apply new position
        self.move(new_x, new_y)

        # Update status bar to show movement using signals
        self.ui_signals.update_status_signal.emit(f"Status: Window moved {direction}")

    def toggle_listening(self):
        """Toggle listening state - works with both CapsLock and UI button"""
        if self.audio_handler.is_listening:
            # Currently listening, so stop
            self.stop_listening()
            print("CapsLock: Stopped listening")
        else:
            # Not listening, so start
            self.start_listening()
            print("CapsLock: Started listening")

    def setup_resize_handle(self):
        """Setup resize handle functionality"""
        # Variables for resize operation
        self.resize_start_x = 0
        self.resize_start_y = 0
        self.resize_start_width = 0
        self.resize_start_height = 0
        self.is_resizing = False

        # Create a custom resize handle widget
        self.resize_handle.installEventFilter(self)

    def eventFilter(self, obj, event):
        """Event filter for resize handle"""
        if obj == self.resize_handle:
            if event.type() == event.MouseButtonPress:
                if event.button() == Qt.LeftButton:
                    self.start_resize(event)
                    return True
            elif event.type() == event.MouseMove:
                if self.is_resizing:
                    self.do_resize(event)
                    return True
            elif event.type() == event.MouseButtonRelease:
                if event.button() == Qt.LeftButton:
                    self.is_resizing = False
                    return True
        return super().eventFilter(obj, event)

    def start_resize(self, event):
        """Start resize operation"""
        self.resize_start_x = event.globalX()
        self.resize_start_y = event.globalY()
        self.resize_start_width = self.width()
        self.resize_start_height = self.height()
        self.is_resizing = True

    def do_resize(self, event):
        """Perform resize operation"""
        # Calculate new size
        new_width = self.resize_start_width + (event.globalX() - self.resize_start_x)
        new_height = self.resize_start_height + (event.globalY() - self.resize_start_y)

        # Apply minimum size constraints
        new_width = max(400, new_width)
        new_height = max(300, new_height)

        # Apply new size
        self.resize(new_width, new_height)

    def create_tooltip(self, widget, text):
        """Create a tooltip for a widget"""
        widget.setToolTip(text)

    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(self, 'Quit', 'Do you want to quit?',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            # Stop cursor timer
            if hasattr(self, 'cursor_timer') and self.cursor_timer:
                self.cursor_timer.stop()

            # Stop keyboard listener
            if hasattr(self, 'keyboard_listener'):
                try:
                    self.keyboard_listener.stop()
                except:
                    pass

            if self.audio_handler.is_listening:
                self.audio_handler.stop_listening() # Ensure graceful shutdown of audio stream
            self.audio_handler.close_audio()

            event.accept()
        else:
            event.ignore()

    # Add mouse event handling for window dragging and click-through
    def mousePressEvent(self, event):
        """Handle mouse press for window dragging and special click behaviors"""
        if event.button() == Qt.LeftButton:
            # Get click position relative to window
            click_pos = event.pos()

            # Define top area for click-to-minimize (top 30 pixels)
            top_area_height = 30

            # Check if click is in the top area for moving window to back
            if click_pos.y() <= top_area_height:
                print("🔄 Click in top area - Moving window to back")
                self.lower()  # Move window to back
                return

            # For dragging, allow on non-interactive areas
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

        elif event.button() == Qt.RightButton:
            # Handle right-click for context menu
            super().mousePressEvent(event)
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def is_interactive_widget(self, widget):
        """Check if a widget is interactive (buttons, dropdowns, etc.)"""
        if widget is None:
            return False

        # List of interactive widget types
        interactive_types = (QPushButton, QComboBox)

        # Check if widget or its parent is interactive
        current_widget = widget
        while current_widget is not None:
            if isinstance(current_widget, interactive_types):
                return True
            if current_widget == self.resize_handle:
                return True
            # The AI response text widget should be non-interactive for click-through
            if current_widget == self.ai_response_text_widget:
                return False
            current_widget = current_widget.parent()

        return False

    def contextMenuEvent(self, event):
        """Handle right-click context menu"""
        context_menu = QMenu(self)

        # Add click-through toggle action
        click_through_action = QAction("Disable Click-Through" if self.click_through_enabled else "Enable Click-Through", self)
        click_through_action.triggered.connect(self.toggle_click_through)
        context_menu.addAction(click_through_action)

        # Add window visibility action
        visibility_action = QAction("Hide Window", self)
        visibility_action.triggered.connect(self.hide_window)
        context_menu.addAction(visibility_action)

        # Add separator
        context_menu.addSeparator()

        # Add help action
        help_action = QAction("Show Controls", self)
        help_action.triggered.connect(self.show_controls_help)
        context_menu.addAction(help_action)

        # Show context menu
        context_menu.exec_(event.globalPos())

    def show_controls_help(self):
        """Show controls help dialog"""
        help_text = """Interview Assistant Controls:

🎯 Window Management:
• Ctrl+Space: Hide/Show window
• Ctrl+Arrow keys: Move window
• Click top area: Move window to back
• Right-click: Context menu
• Drag resize handle: Resize window

🎤 Audio Controls:
• CapsLock: Start/Stop listening
• Start/Stop buttons: Manual control

🔧 Advanced Features:
• Click-through mode: ENABLED by default
  - Clicks pass through to underlying apps
  - Buttons/controls still work normally
• Window transparency and stealth features
• Screen capture hiding

💡 Usage Tips:
• Click anywhere on text area → passes to Google Meet
• Use buttons normally → they always work
• Right-click to disable click-through if needed"""

        QMessageBox.information(self, "Controls Help", help_text)

if __name__ == "__main__":
    # Create QApplication
    qt_app = QApplication(sys.argv)
    qt_app.setQuitOnLastWindowClosed(True)

    # Set application properties
    qt_app.setApplicationName("Real-time Interview Assistant")
    qt_app.setApplicationVersion("1.0")

    # Create and show main window
    app = InterviewApp()

    # Add tooltip for resize handle
    app.create_tooltip(app.resize_handle, "Drag to resize window\nCtrl+Arrow keys: Move window\nCtrl+Space: Hide/Show window\nCapsLock: Start/Stop listening\nClick top area: Move to back\nClick-through: Enabled by default\nRight-click: Context menu")

    app.show()

    print("🤖 Real-time Interview Assistant Started!")
    print("🥷 Stealth Features:")
    print(f"   • Window transparency: {WINDOW_TRANSPARENCY}")
    print(f"   • Screen capture hiding: {ENABLE_SCREEN_CAPTURE_HIDING}")
    print(f"   • Taskbar hiding: {ENABLE_TASKBAR_HIDING}")
    print("📋 Controls:")
    print("   • Ctrl+Arrow keys: Move window")
    print("   • Ctrl+Space: Hide/Show window")
    print("   • CapsLock: Start/Stop listening")
    print("   • Click top area: Move window to back")
    print("   • Click-through: Enabled by default")
    print("   • Right-click: Context menu to toggle features")
    print("   • Drag resize handle to resize window")

    # Only run mainloop if app initialization was successful (API key loaded)
    if hasattr(app, 'api_client') and app.api_client:
        sys.exit(qt_app.exec_())
    else:
        print("Application failed to initialize due to API key error.")
        sys.exit(1)